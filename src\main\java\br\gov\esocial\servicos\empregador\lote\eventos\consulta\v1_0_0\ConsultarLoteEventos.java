package br.gov.esocial.servicos.empregador.lote.eventos.consulta.v1_0_0;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;

/**
 * <p>Classe Java para anonymous complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="consulta" minOccurs="0"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="protocoloEnvio" minOccurs="0"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;sequence&gt;
 *                             &lt;any processContents='lax' minOccurs="0"/&gt;
 *                           &lt;/sequence&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "consulta"
})
@XmlRootElement(name = "ConsultarLoteEventos")
public class ConsultarLoteEventos {

    protected ConsultarLoteEventos.Consulta consulta;

    /**
     * Obtém o valor da propriedade consulta.
     * 
     * @return
     *     possible object is
     *     {@link ConsultarLoteEventos.Consulta }
     *     
     */
    public ConsultarLoteEventos.Consulta getConsulta() {
        return consulta;
    }

    /**
     * Define o valor da propriedade consulta.
     * 
     * @param value
     *     allowed object is
     *     {@link ConsultarLoteEventos.Consulta }
     *     
     */
    public void setConsulta(ConsultarLoteEventos.Consulta value) {
        this.consulta = value;
    }


    /**
     * <p>Classe Java para anonymous complex type.
     * 
     * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
     * 
     * <pre>
     * &lt;complexType&gt;
     *   &lt;complexContent&gt;
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
     *       &lt;sequence&gt;
     *         &lt;element name="protocoloEnvio" minOccurs="0"&gt;
     *           &lt;complexType&gt;
     *             &lt;complexContent&gt;
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
     *                 &lt;sequence&gt;
     *                   &lt;any processContents='lax' minOccurs="0"/&gt;
     *                 &lt;/sequence&gt;
     *               &lt;/restriction&gt;
     *             &lt;/complexContent&gt;
     *           &lt;/complexType&gt;
     *         &lt;/element&gt;
     *       &lt;/sequence&gt;
     *     &lt;/restriction&gt;
     *   &lt;/complexContent&gt;
     * &lt;/complexType&gt;
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "protocoloEnvio"
    })
    public static class Consulta {

        protected ConsultarLoteEventos.Consulta.ProtocoloEnvio protocoloEnvio;

        /**
         * Obtém o valor da propriedade protocoloEnvio.
         * 
         * @return
         *     possible object is
         *     {@link ConsultarLoteEventos.Consulta.ProtocoloEnvio }
         *     
         */
        public ConsultarLoteEventos.Consulta.ProtocoloEnvio getProtocoloEnvio() {
            return protocoloEnvio;
        }

        /**
         * Define o valor da propriedade protocoloEnvio.
         * 
         * @param value
         *     allowed object is
         *     {@link ConsultarLoteEventos.Consulta.ProtocoloEnvio }
         *     
         */
        public void setProtocoloEnvio(ConsultarLoteEventos.Consulta.ProtocoloEnvio value) {
            this.protocoloEnvio = value;
        }


        /**
         * <p>Classe Java para anonymous complex type.
         * 
         * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
         * 
         * <pre>
         * &lt;complexType&gt;
         *   &lt;complexContent&gt;
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
         *       &lt;sequence&gt;
         *         &lt;any processContents='lax' minOccurs="0"/&gt;
         *       &lt;/sequence&gt;
         *     &lt;/restriction&gt;
         *   &lt;/complexContent&gt;
         * &lt;/complexType&gt;
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "any"
        })
        public static class ProtocoloEnvio {

            @XmlElement(name = "any")
            protected org.w3c.dom.Element any;

            /**
             * Obtém o valor da propriedade any.
             * 
             * @return
             *     possible object is
             *     {@link org.w3c.dom.Element }
             *     
             */
            public org.w3c.dom.Element getAny() {
                return any;
            }

            /**
             * Define o valor da propriedade any.
             * 
             * @param value
             *     allowed object is
             *     {@link org.w3c.dom.Element }
             *     
             */
            public void setAny(org.w3c.dom.Element value) {
                this.any = value;
            }

        }

    }

}

package com.br.sasw.esocial_novo.config;

import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.HttpProcessor;
import org.apache.http.protocol.HttpProcessorBuilder;
import org.apache.http.protocol.RequestTargetHost;
import org.apache.http.protocol.RequestUserAgent;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.transport.http.HttpComponentsMessageSender;

import javax.net.ssl.SSLContext;
import java.security.KeyStore;

@Configuration
public class SoapConfig {

    @Bean
    public Jaxb2Marshaller marshaller() {
        Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
        marshaller.setContextPath("br.gov.esocial.servicos.empregador.lote.eventos.envio.v1_1_0");
        return marshaller;
    }

    @Bean
    public Jaxb2Marshaller consultaMarshaller() {
        Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
        marshaller.setContextPath("br.gov.esocial.servicos.empregador.lote.eventos.consulta.v1_1_0");
        return marshaller;
    }

    @Bean
    public WebServiceTemplate webServiceTemplate() throws Exception {
        WebServiceTemplate webServiceTemplate = new WebServiceTemplate();
        webServiceTemplate.setMarshaller(marshaller());
        webServiceTemplate.setUnmarshaller(marshaller());
        webServiceTemplate.setDefaultUri("https://webservices.envio.esocial.gov.br/servicos/empregador/enviarloteeventos/WsEnviarLoteEventos.svc");
        webServiceTemplate.setMessageSender(httpComponentsMessageSender());
        return webServiceTemplate;
    }

    @Bean
    public WebServiceTemplate consultaWebServiceTemplate() throws Exception {
        WebServiceTemplate webServiceTemplate = new WebServiceTemplate();
        webServiceTemplate.setMarshaller(consultaMarshaller());
        webServiceTemplate.setUnmarshaller(consultaMarshaller());
        webServiceTemplate.setDefaultUri("https://webservices.consulta.esocial.gov.br/servicos/empregador/consultarloteeventos/WsConsultarLoteEventos.svc");
        webServiceTemplate.setMessageSender(httpComponentsMessageSender());
        return webServiceTemplate;
    }

    @Bean
    public HttpComponentsMessageSender httpComponentsMessageSender() throws Exception {
        // Configuração do KeyStore (seu certificado digital)
        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        keyStore.load(new ClassPathResource("go-federal.pfx").getInputStream(), "Federallcom13".toCharArray());

        // Configuração do SSLContext
        SSLContext sslContext = SSLContextBuilder.create()
                .loadKeyMaterial(keyStore, "Federallcom13".toCharArray())
                .loadTrustMaterial(null, (chain, authType) -> true) // Aceita todos os certificados - em produção, configure um TrustStore adequado
                .build();

        // Configuração personalizada do HttpProcessor para evitar duplicação de headers
        HttpProcessor httpProcessor = HttpProcessorBuilder.create()
                .add(new RequestTargetHost())
                .add(new RequestUserAgent("Spring WS"))
                // Removemos RequestContent que adiciona Content-Length automaticamente
                .build();

        // Configuração do HttpClient
        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLSocketFactory(new SSLConnectionSocketFactory(sslContext))
                .setHttpProcessor(httpProcessor)
                .disableContentCompression()
                .build();

        HttpComponentsMessageSender messageSender = new HttpComponentsMessageSender();
        messageSender.setHttpClient(httpClient);

        return messageSender;
    }
}
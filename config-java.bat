@echo off
setlocal enabledelayedexpansion

echo ========================================
echo  Configurador do Java Local
echo ========================================
echo.
echo Este script ajuda a configurar o caminho do Java local
echo para evitar conflitos com outras instalacoes do sistema.
echo.

REM Mostra o Java atual do sistema (se houver)
echo Java atual do sistema:
java -version 2>nul
if %ERRORLEVEL% neq 0 (
    echo Nenhum Java encontrado no PATH do sistema
)
echo.

echo ========================================
echo  Locais comuns de instalacao do Java:
echo ========================================
echo.
echo 1. C:\Program Files\Java\jdk-17
echo 2. C:\Program Files\Java\jdk-17.0.x
echo 3. C:\Program Files\Eclipse Adoptium\jdk-17.x.x-hotspot
echo 4. C:\java\jdk-17
echo 5. C:\tools\java\jdk-17
echo.

echo Verificando instalacoes existentes...
echo.

REM Verifica alguns caminhos comuns
set "FOUND_JAVA="

if exist "C:\Program Files\Java\" (
    echo Encontrado diretorio: C:\Program Files\Java\
    dir "C:\Program Files\Java\" /b | findstr /i jdk
)

if exist "C:\Program Files\Eclipse Adoptium\" (
    echo Encontrado diretorio: C:\Program Files\Eclipse Adoptium\
    dir "C:\Program Files\Eclipse Adoptium\" /b | findstr /i jdk
)

if exist "C:\java\" (
    echo Encontrado diretorio: C:\java\
    dir "C:\java\" /b | findstr /i jdk
)

echo.
echo ========================================
echo  Instrucoes de configuracao:
echo ========================================
echo.
echo 1. Identifique o caminho completo da sua instalacao do Java 17
echo 2. Edite o arquivo 'run-app.bat'
echo 3. Modifique a linha: set "LOCAL_JAVA_HOME=C:\java\jdk-17"
echo 4. Substitua pelo caminho correto da sua instalacao
echo.
echo Exemplo:
echo   set "LOCAL_JAVA_HOME=C:\Program Files\Java\jdk-17.0.9"
echo.
echo ========================================
echo  Teste de configuracao:
echo ========================================
echo.
set /p JAVA_PATH="Digite o caminho do seu Java 17 para testar: "

if "%JAVA_PATH%"=="" (
    echo Nenhum caminho fornecido. Saindo...
    pause
    exit /b 0
)

if not exist "%JAVA_PATH%" (
    echo ERRO: Diretorio nao encontrado: %JAVA_PATH%
    pause
    exit /b 1
)

if not exist "%JAVA_PATH%\bin\java.exe" (
    echo ERRO: java.exe nao encontrado em: %JAVA_PATH%\bin\
    pause
    exit /b 1
)

echo.
echo Testando Java em: %JAVA_PATH%
echo.
"%JAVA_PATH%\bin\java.exe" -version

echo.
echo ========================================
echo  Configuracao testada com sucesso!
echo ========================================
echo.
echo Agora edite o arquivo 'run-app.bat' e configure:
echo   set "LOCAL_JAVA_HOME=%JAVA_PATH%"
echo.
pause

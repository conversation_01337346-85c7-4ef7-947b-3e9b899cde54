package com.br.sasw.esocial_novo.util;

import lombok.experimental.UtilityClass;
import org.w3c.dom.Document;
import org.xml.sax.InputSource;

import javax.xml.crypto.dsig.*;
import javax.xml.crypto.dsig.dom.DOMSignContext;
import javax.xml.crypto.dsig.keyinfo.KeyInfo;
import javax.xml.crypto.dsig.keyinfo.KeyInfoFactory;
import javax.xml.crypto.dsig.keyinfo.X509Data;
import javax.xml.crypto.dsig.spec.C14NMethodParameterSpec;
import javax.xml.crypto.dsig.spec.TransformParameterSpec;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.StringWriter;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@UtilityClass
public class XmlSigner {

    Map<String, Certificado> certicate = new HashMap<>();

    public String sign(String xml, String empresa) {
        try {
            certicate.put("federal", new Certificado("C:\\certificados-chaves\\FEDERAL\\FEDERAL_GOIANIA.pfx", "Federallcom13"));
            String caminhoCertificado = certicate.get(empresa).getLocal();
            String senhaCertificado = certicate.get(empresa).getSenha();
            String alias = null;
            // Carrega o keystore
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(new FileInputStream(caminhoCertificado), senhaCertificado.toCharArray());

            // Obtém o alias
            for (var e = keyStore.aliases(); e.hasMoreElements(); ) {
                alias = e.nextElement();
            }

            // Obtém chave e certificado
            PrivateKey chavePrivada = (PrivateKey) keyStore.getKey(alias, senhaCertificado.toCharArray());
            X509Certificate certificado = (X509Certificate) keyStore.getCertificate(alias);

            // Carrega o XML e ativa namespace
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            dbf.setNamespaceAware(true);
            Document doc = dbf.newDocumentBuilder().parse(new InputSource(new ByteArrayInputStream(xml.getBytes("utf-8"))));

            // Cria a fábrica de assinatura
            XMLSignatureFactory sigFactory = XMLSignatureFactory.getInstance("DOM");

            // Cria a referência com os métodos exigidos
            Reference ref = sigFactory.newReference("",
                    sigFactory.newDigestMethod(DigestMethod.SHA256, null),
                    Arrays.asList(
                            sigFactory.newTransform(Transform.ENVELOPED, (TransformParameterSpec) null),
                            sigFactory.newTransform(CanonicalizationMethod.INCLUSIVE, (C14NMethodParameterSpec) null)
                    ),
                    null,
                    null
            );

            // Define as informações da assinatura
            SignedInfo signedInfo = sigFactory.newSignedInfo(
                    sigFactory.newCanonicalizationMethod(CanonicalizationMethod.INCLUSIVE, (C14NMethodParameterSpec) null),
                    sigFactory.newSignatureMethod(SignatureMethod.RSA_SHA256, null),
                    Collections.singletonList(ref)
            );

            // Cria a KeyInfo com o certificado do usuário final (EndCertOnly)
            KeyInfoFactory kif = sigFactory.getKeyInfoFactory();
            X509Data x509Data = kif.newX509Data(Collections.singletonList(certificado));
            KeyInfo keyInfo = kif.newKeyInfo(Collections.singletonList(x509Data));

            // Define o local da assinatura (pai do nó a ser assinado)
            DOMSignContext dsc = new DOMSignContext(chavePrivada, doc.getDocumentElement());

            // Cria e aplica a assinatura
            XMLSignature signature = sigFactory.newXMLSignature(signedInfo, keyInfo);
            signature.sign(dsc);

            // Salva o resultado
            Transformer transformer = TransformerFactory.newInstance().newTransformer();
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.INDENT, "no");
            transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "1");
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.ENCODING, "UTF-8");
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.METHOD, "xml");
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.STANDALONE, "no");
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.OMIT_XML_DECLARATION, "yes");

            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(doc), new StreamResult(writer));
            return writer.toString();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}

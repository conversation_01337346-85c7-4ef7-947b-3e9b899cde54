package br.gov.esocial.servicos.empregador.lote.eventos.consulta.v1_0_0;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;

/**
 * <p>Classe Java para anonymous complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="ConsultarLoteEventosResult" minOccurs="0"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;any processContents='lax' minOccurs="0"/&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "consultarLoteEventosResult"
})
@XmlRootElement(name = "ConsultarLoteEventosResponse")
public class ConsultarLoteEventosResponse {

    @XmlElement(name = "ConsultarLoteEventosResult")
    protected ConsultarLoteEventosResponse.ConsultarLoteEventosResult consultarLoteEventosResult;

    /**
     * Obtém o valor da propriedade consultarLoteEventosResult.
     * 
     * @return
     *     possible object is
     *     {@link ConsultarLoteEventosResponse.ConsultarLoteEventosResult }
     *     
     */
    public ConsultarLoteEventosResponse.ConsultarLoteEventosResult getConsultarLoteEventosResult() {
        return consultarLoteEventosResult;
    }

    /**
     * Define o valor da propriedade consultarLoteEventosResult.
     * 
     * @param value
     *     allowed object is
     *     {@link ConsultarLoteEventosResponse.ConsultarLoteEventosResult }
     *     
     */
    public void setConsultarLoteEventosResult(ConsultarLoteEventosResponse.ConsultarLoteEventosResult value) {
        this.consultarLoteEventosResult = value;
    }


    /**
     * <p>Classe Java para anonymous complex type.
     * 
     * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
     * 
     * <pre>
     * &lt;complexType&gt;
     *   &lt;complexContent&gt;
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
     *       &lt;sequence&gt;
     *         &lt;any processContents='lax' minOccurs="0"/&gt;
     *       &lt;/sequence&gt;
     *     &lt;/restriction&gt;
     *   &lt;/complexContent&gt;
     * &lt;/complexType&gt;
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "any"
    })
    public static class ConsultarLoteEventosResult {

        @XmlElement(name = "any")
        protected org.w3c.dom.Element any;

        /**
         * Obtém o valor da propriedade any.
         * 
         * @return
         *     possible object is
         *     {@link org.w3c.dom.Element }
         *     
         */
        public org.w3c.dom.Element getAny() {
            return any;
        }

        /**
         * Define o valor da propriedade any.
         * 
         * @param value
         *     allowed object is
         *     {@link org.w3c.dom.Element }
         *     
         */
        public void setAny(org.w3c.dom.Element value) {
            this.any = value;
        }

    }

}

@echo off
setlocal enabledelayedexpansion

echo ========================================
echo  Diagnostico do Ambiente
echo ========================================
echo.

REM Verifica permissoes de administrador
net session >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo [OK] Executando como Administrador
) else (
    echo [AVISO] NAO esta executando como Administrador
    echo         Alguns problemas podem ser resolvidos executando como Admin
)
echo.

REM Verifica Java do sistema
echo Verificando Java do sistema...
java -version >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo [OK] Java encontrado no sistema:
    java -version
) else (
    echo [INFO] Nenhum Java encontrado no PATH do sistema
)
echo.

REM Verifica Maven do sistema
echo Verificando Maven do sistema...
mvn -version >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo [OK] Maven encontrado no sistema:
    mvn -version
) else (
    echo [INFO] Maven nao encontrado no PATH do sistema
)
echo.

REM Verifica Java local configurado
set "LOCAL_JAVA_HOME=C:\Program Files\Java\jdk-17.0.15+6"
echo Verificando Java local configurado: %LOCAL_JAVA_HOME%
if exist "%LOCAL_JAVA_HOME%" (
    if exist "%LOCAL_JAVA_HOME%\bin\java.exe" (
        echo [OK] Java local encontrado:
        "%LOCAL_JAVA_HOME%\bin\java.exe" -version
    ) else (
        echo [ERRO] java.exe nao encontrado em %LOCAL_JAVA_HOME%\bin\
    )
) else (
    echo [ERRO] Diretorio nao encontrado: %LOCAL_JAVA_HOME%
    echo        Configure o caminho correto nos scripts
)
echo.

REM Verifica arquivos do projeto
echo Verificando arquivos do projeto...
if exist "pom.xml" (
    echo [OK] pom.xml encontrado
) else (
    echo [ERRO] pom.xml nao encontrado - execute no diretorio do projeto
)

if exist "mvnw.cmd" (
    echo [OK] mvnw.cmd encontrado
) else (
    echo [ERRO] mvnw.cmd nao encontrado
)

if exist "src\main\java" (
    echo [OK] Diretorio src\main\java encontrado
) else (
    echo [ERRO] Diretorio src\main\java nao encontrado
)
echo.

REM Verifica processos Java em execucao
echo Verificando processos Java em execucao...
tasklist /FI "IMAGENAME eq java.exe" 2>nul | find /i "java.exe" >nul
if %ERRORLEVEL% equ 0 (
    echo [AVISO] Processos Java em execucao:
    tasklist /FI "IMAGENAME eq java.exe" | findstr java.exe
    echo         Feche outras aplicacoes Java se houver problemas
) else (
    echo [OK] Nenhum processo Java em execucao
)
echo.

REM Verifica espaco em disco
echo Verificando espaco em disco...
for /f "tokens=3" %%a in ('dir /-c ^| find "bytes free"') do set "FREE_SPACE=%%a"
if defined FREE_SPACE (
    echo [INFO] Espaco livre: %FREE_SPACE% bytes
) else (
    echo [INFO] Nao foi possivel verificar espaco em disco
)
echo.

REM Verifica conectividade
echo Verificando conectividade com repositorios Maven...
ping -n 1 repo1.maven.org >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo [OK] Conectividade com repo1.maven.org
) else (
    echo [AVISO] Problemas de conectividade com repo1.maven.org
    echo         Verifique sua conexao com internet
)
echo.

REM Verifica diretorio temporario
echo Verificando diretorio temporario...
if exist "%TEMP%" (
    echo [OK] Diretorio temporario: %TEMP%
    REM Tenta criar um arquivo de teste
    echo teste > "%TEMP%\teste_maven.tmp" 2>nul
    if exist "%TEMP%\teste_maven.tmp" (
        echo [OK] Permissao de escrita no diretorio temporario
        del "%TEMP%\teste_maven.tmp" >nul 2>&1
    ) else (
        echo [ERRO] Sem permissao de escrita no diretorio temporario
    )
) else (
    echo [ERRO] Diretorio temporario nao encontrado
)
echo.

echo ========================================
echo  Recomendacoes
echo ========================================
echo.

if not exist "%LOCAL_JAVA_HOME%" (
    echo 1. Configure o Java local nos scripts
    echo    Edite run-app.bat e modifique LOCAL_JAVA_HOME
    echo.
)

net session >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo 2. Tente executar como Administrador
    echo    Use run-app-admin.bat
    echo.
)

mvn -version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo 3. Considere usar a versao portatil
    echo    Use run-app-portable.bat
    echo.
)

echo 4. Se persistirem problemas:
echo    - Reinicie o computador
echo    - Feche outras aplicacoes Java
echo    - Verifique antivirus/firewall
echo    - Use run-app-portable.bat
echo.

pause

@echo off
setlocal enabledelayedexpansion

echo ========================================
echo  eSocial Novo - Script de Execucao
echo ========================================

REM Configuracao do Java local
REM Modifique o caminho abaixo para apontar para sua instalacao local do Java 17
set "LOCAL_JAVA_HOME=C:\Program Files\Java\jdk-17.0.15+6"

REM Verifica se o diretorio do Java existe
if not exist "%LOCAL_JAVA_HOME%" (
    echo ERRO: Diretorio do Java nao encontrado: %LOCAL_JAVA_HOME%
    echo.
    echo Por favor, edite este script e configure o LOCAL_JAVA_HOME
    echo para apontar para sua instalacao local do Java 17.
    echo.
    echo Exemplo: set "LOCAL_JAVA_HOME=C:\Program Files\Java\jdk-17"
    echo.
    pause
    exit /b 1
)

REM Verifica se o java.exe existe
if not exist "%LOCAL_JAVA_HOME%\bin\java.exe" (
    echo ERRO: java.exe nao encontrado em: %LOCAL_JAVA_HOME%\bin\
    echo.
    echo Verifique se o caminho do Java esta correto.
    echo.
    pause
    exit /b 1
)

REM Configura as variaveis de ambiente para usar o Java local
set "JAVA_HOME=%LOCAL_JAVA_HOME%"
set "PATH=%LOCAL_JAVA_HOME%\bin;%PATH%"

echo Usando Java: %JAVA_HOME%
echo.

REM Verifica a versao do Java
echo Verificando versao do Java...
"%JAVA_HOME%\bin\java.exe" -version
echo.

REM Limpa processos Java que podem estar travando
echo Verificando processos Java em execucao...
tasklist /FI "IMAGENAME eq java.exe" 2>nul | find /i "java.exe" >nul
if %ERRORLEVEL% equ 0 (
    echo Encontrados processos Java em execucao. Aguarde...
    timeout /t 3 /nobreak >nul
)

REM Limpa diretorio target se existir
if exist "target" (
    echo Limpando diretorio target...
    rmdir /s /q "target" 2>nul
    timeout /t 2 /nobreak >nul
)

REM Compila a aplicacao
echo ========================================
echo  Compilando a aplicacao...
echo ========================================

REM Primeira tentativa com mvnw
echo Tentativa 1: Usando Maven Wrapper...
call mvnw.cmd clean package -DskipTests -Dmaven.repo.local=.m2\repository

if %ERRORLEVEL% neq 0 (
    echo.
    echo Maven Wrapper falhou. Tentando alternativas...

    REM Verifica se Maven esta instalado no sistema
    mvn -version >nul 2>&1
    if %ERRORLEVEL% equ 0 (
        echo Tentativa 2: Usando Maven do sistema...
        mvn clean package -DskipTests -Dmaven.repo.local=.m2\repository

        if %ERRORLEVEL% neq 0 (
            echo.
            echo ERRO: Falha na compilacao com Maven do sistema!
            echo.
            echo Possiveis solucoes:
            echo 1. Execute como Administrador
            echo 2. Feche outras aplicacoes Java
            echo 3. Reinicie o computador
            echo 4. Verifique conexao com internet
            echo.
            pause
            exit /b 1
        )
    ) else (
        echo.
        echo ERRO: Falha na compilacao da aplicacao!
        echo.
        echo Possiveis solucoes:
        echo 1. Execute como Administrador
        echo 2. Feche outras aplicacoes Java
        echo 3. Reinicie o computador
        echo 4. Verifique conexao com internet
        echo 5. Instale Maven manualmente
        echo.
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo  Compilacao concluida com sucesso!
echo ========================================

REM Encontra o JAR gerado
set "JAR_FILE="
for %%f in (target\*.jar) do (
    if not "%%~nf"=="%%~nf.original" (
        set "JAR_FILE=%%f"
    )
)

if "%JAR_FILE%"=="" (
    echo ERRO: JAR nao encontrado no diretorio target!
    pause
    exit /b 1
)

echo JAR encontrado: %JAR_FILE%
echo.

REM Executa a aplicacao
echo ========================================
echo  Iniciando a aplicacao...
echo ========================================
echo.
echo Para parar a aplicacao, pressione Ctrl+C
echo.

"%JAVA_HOME%\bin\java.exe" -jar "%JAR_FILE%"

echo.
echo ========================================
echo  Aplicacao finalizada
echo ========================================
pause

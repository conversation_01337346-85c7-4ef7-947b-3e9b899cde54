<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions targetNamespace="http://www.esocial.gov.br/servicos/empregador/enviarloteeventos/v1_1_0"
                  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex"
                  xmlns:i0="http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/v1_1_0"
                  xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
                  xmlns:wsa10="http://www.w3.org/2005/08/addressing"
                  xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy"
                  xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy"
                  xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract"
                  xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/"
                  xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing"
                  xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:tns="http://www.esocial.gov.br/servicos/empregador/enviarloteeventos/v1_1_0"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl"
                  xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/">
    <wsp:Policy wsu:Id="WsEnviarLoteEventos_policy">
        <wsp:ExactlyOne>
            <wsp:All>
                <sp:TransportBinding xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy">
                    <wsp:Policy>
                        <sp:TransportToken>
                            <wsp:Policy>
                                <sp:HttpsToken RequireClientCertificate="true"/>
                            </wsp:Policy>
                        </sp:TransportToken>
                        <sp:AlgorithmSuite>
                            <wsp:Policy>
                                <sp:Basic256/>
                            </wsp:Policy>
                        </sp:AlgorithmSuite>
                        <sp:Layout>
                            <wsp:Policy>
                                <sp:Strict/>
                            </wsp:Policy>
                        </sp:Layout>
                    </wsp:Policy>
                </sp:TransportBinding>
            </wsp:All>
        </wsp:ExactlyOne>
    </wsp:Policy>
    <wsdl:import namespace="http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/v1_1_0"
                 location="WsEnviarLoteEventos.wsdl"/>
    <wsdl:types/>
    <wsdl:binding name="WsEnviarLoteEventos" type="i0:ServicoEnviarLoteEventos">
        <wsp:PolicyReference URI="#WsEnviarLoteEventos_policy"/>
        <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="EnviarLoteEventos">
            <soap:operation
                    soapAction="http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/v1_1_0/ServicoEnviarLoteEventos/EnviarLoteEventos"
                    style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
</wsdl:definitions>
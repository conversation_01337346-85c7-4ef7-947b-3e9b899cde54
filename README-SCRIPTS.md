# Scripts para Execução da Aplicação eSocial Novo

Este conjunto de scripts permite compilar e executar a aplicação usando uma instalação local do Java, evitando conflitos com outras aplicações do sistema.

## Pré-requisitos

- Java 17 instalado localmente (não precisa estar no PATH do sistema)
- Maven (o projeto já inclui o Maven Wrapper - mvnw.cmd)

## Scripts Disponíveis

### 1. `diagnostico.bat` - Diagnóstico do Ambiente
**Use este script primeiro para identificar problemas.**

- Verifica Java local e do sistema
- Verifica Maven e conectividade
- Identifica problemas de permissão
- Fornece recomendações específicas

**Como usar:**
```cmd
diagnostico.bat
```

### 2. `config-java.bat` - Configurador do Java Local
**Ajuda a identificar e testar sua instalação do Java.**

- Mostra locais comuns onde o Java pode estar instalado
- Permite testar se uma instalação do Java está funcionando
- Fornece instruções de como configurar os outros scripts

### 3. `run-app.bat` - Script Principal
**Script completo que compila e executa a aplicação.**

- Compila a aplicação usando Maven Wrapper ou Maven do sistema
- Executa o JAR gerado
- Inclui fallbacks para problemas comuns

### 4. `run-app-admin.bat` - Execução como Administrador
**Força execução com privilégios de administrador.**

- Resolve problemas de permissão
- Chama automaticamente o script principal

### 5. `run-app-portable.bat` - Versão Portátil
**Baixa e usa Maven portátil automaticamente.**

- Não depende de Maven instalado no sistema
- Baixa Maven automaticamente se necessário
- Ideal para ambientes restritivos

### 6. `compile-only.bat` - Apenas Compilação
**Script para compilar a aplicação sem executá-la.**

- Útil durante o desenvolvimento
- Inclui fallbacks como os outros scripts

## Configuração Inicial

### Passo 1: Execute o diagnóstico
```cmd
diagnostico.bat
```
Este script identificará problemas e dará recomendações específicas.

### Passo 2: Escolha a estratégia baseada no diagnóstico

**Se tiver problemas de permissão:**
```cmd
run-app-admin.bat
```

**Se não tiver Maven instalado ou houver conflitos:**
```cmd
run-app-portable.bat
```

**Para uso normal (após configurar Java):**
```cmd
run-app.bat
```

### Passo 3: Configure o Java local (se necessário)
Edite os scripts e modifique a linha:
```batch
set "LOCAL_JAVA_HOME=C:\java\jdk-17"
```

**Exemplos de caminhos comuns:**
- `C:\Program Files\Java\jdk-17.0.9`
- `C:\Program Files\Eclipse Adoptium\jdk-********-hotspot`
- `C:\java\jdk-17`

## Características dos Scripts

### Verificações de Segurança
- Verificam se o Java existe no caminho especificado
- Validam se o java.exe está presente
- Mostram a versão do Java sendo usada
- Verificam se a compilação foi bem-sucedida

### Isolamento do Java
- Configuram `JAVA_HOME` temporariamente
- Modificam o `PATH` apenas durante a execução
- Não afetam outras aplicações do sistema

### Tratamento de Erros
- Mensagens claras em caso de erro
- Pausam para permitir leitura das mensagens
- Códigos de saída apropriados

## Solução de Problemas

### Erro: "Access to the path is denied" (Maven Wrapper)
**Solução mais comum:**
```cmd
run-app-admin.bat
```

**Alternativas:**
1. Use a versão portátil: `run-app-portable.bat`
2. Feche outras aplicações Java
3. Reinicie o computador

### Erro: "Cannot start maven from wrapper"
**Soluções em ordem de preferência:**
1. Execute `diagnostico.bat` para identificar o problema
2. Use `run-app-portable.bat` (baixa Maven automaticamente)
3. Execute como administrador: `run-app-admin.bat`
4. Instale Maven manualmente no sistema

### Erro: "Diretório do Java não encontrado"
1. Execute `diagnostico.bat` para verificar configuração
2. Use `config-java.bat` para encontrar instalações
3. Edite os scripts com o caminho correto

### Problemas de conectividade
- Verifique conexão com internet
- Configure proxy se necessário
- Use `run-app-portable.bat` que usa repositório local

## Informações Técnicas

- **Versão do Java requerida:** 17+
- **Tipo de aplicação:** Spring Boot
- **Classe principal:** `com.br.sasw.esocial_novo.EsocialNovoApplication`
- **Porta padrão:** 8080 (Spring Boot padrão)
- **Perfil Maven:** Usa o Spring Boot Maven Plugin para gerar JAR executável

## Notas Importantes

1. Os scripts não modificam configurações globais do sistema
2. Cada execução usa um ambiente isolado
3. É seguro ter múltiplas versões do Java no sistema
4. Os scripts podem ser personalizados conforme necessário
5. Para parar a aplicação, use `Ctrl+C` no console

package com.br.sasw.esocial_novo;

import com.br.sasw.esocial_novo.esocial.EsocialService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest
@TestPropertySource(properties = {
    "logging.level.org.springframework.ws.client.MessageTracing=DEBUG",
    "logging.level.org.apache.http.wire=DEBUG"
})
public class ConsultaEsocialTest {

    @Autowired
    private EsocialService esocialService;

    @Test
    public void testConsultaProtocolo() {
        // Protocolo de exemplo - substitua por um protocolo válido para teste real
        String protocolo = "1.1.202506.0000000011770344917";
        
        try {
            EsocialService.ConsultaResponse response = esocialService.get(protocolo);
            
            System.out.println("XML enviado:");
            System.out.println(response.getXmlEnviado());
            
            System.out.println("\nResposta recebida:");
            if (response.getConsulta() != null && response.getConsulta().getConsultarLoteEventosResult() != null) {
                System.out.println("Consulta realizada com sucesso!");
                if (response.getConsulta().getConsultarLoteEventosResult().getAny() != null) {
                    System.out.println("Resultado: " + response.getConsulta().getConsultarLoteEventosResult().getAny().getTextContent());
                }
            } else {
                System.out.println("Resposta vazia ou nula");
            }
            
        } catch (Exception e) {
            System.err.println("Erro durante a consulta: " + e.getMessage());
            e.printStackTrace();
        }
    }
}

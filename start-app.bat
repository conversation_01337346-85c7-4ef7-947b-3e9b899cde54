@echo off
setlocal enabledelayedexpansion

echo ========================================
echo  eSocial Novo - Iniciar Aplicacao
echo ========================================

REM Configuracao do Java local
set "LOCAL_JAVA_HOME=C:\Program Files\Java\jdk-17.0.15+6"

REM Configura as variaveis de ambiente
set "JAVA_HOME=%LOCAL_JAVA_HOME%"
set "PATH=%LOCAL_JAVA_HOME%\bin;%PATH%"

echo Usando Java: %JAVA_HOME%
echo.

REM Verifica se JAR existe
if not exist "target\esocial-novo-0.0.1-SNAPSHOT.jar" (
    echo ERRO: Aplicacao nao foi compilada ainda!
    echo.
    echo Execute primeiro um dos scripts de compilacao:
    echo - run-app-final.bat
    echo - run-app-simple.bat
    echo - run-app-robust.bat
    echo.
    pause
    exit /b 1
)

echo [OK] JAR encontrado: target\esocial-novo-0.0.1-SNAPSHOT.jar
echo.

REM Mostra informacoes do JAR
echo Informacoes do JAR:
dir "target\esocial-novo-0.0.1-SNAPSHOT.jar"
echo.

echo ========================================
echo  Iniciando aplicacao eSocial Novo
echo ========================================
echo.
echo INFORMACOES IMPORTANTES:
echo.
echo 1. A aplicacao pode demorar 30-60 segundos para iniciar
echo 2. Aguarde a mensagem "Started EsocialNovoApplication"
echo 3. A aplicacao ficara disponivel em:
echo    http://localhost:8080
echo.
echo 4. Para parar a aplicacao, pressione Ctrl+C
echo.
echo 5. Logs da aplicacao serao mostrados abaixo
echo.
echo ========================================
echo  Iniciando...
echo ========================================
echo.

REM Executa a aplicacao
"%JAVA_HOME%\bin\java.exe" -jar "target\esocial-novo-0.0.1-SNAPSHOT.jar"

echo.
echo ========================================
echo  Aplicacao encerrada
echo ========================================
echo.
echo A aplicacao eSocial Novo foi encerrada.
echo Para reiniciar, execute este script novamente.
echo.
pause

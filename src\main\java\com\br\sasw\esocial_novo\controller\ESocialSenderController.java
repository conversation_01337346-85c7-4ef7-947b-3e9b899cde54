package com.br.sasw.esocial_novo.controller;

import br.gov.esocial.servicos.empregador.lote.eventos.envio.v1_1_0.EnviarLoteEventosResponse;
import com.br.sasw.esocial_novo.esocial.EsocialService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.br.sasw.esocial_novo.esocial.EsocialService.FullResponse;

@RestController
@RequiredArgsConstructor
public class ESocialSenderController {

    private final EsocialService esocialService;

    @PostMapping(path = "/envio-esocial")
    private ResponseEntity<FullResponse> send(@RequestBody String xml,
                                        String id,
                                        @RequestParam String empresa,
                                        @RequestParam String nrInscEmpregador,
                                        @RequestParam String tpInscEmpregador,
										@RequestParam String nrInscTransmissor,
                                        @RequestParam String tpInscTransmissor,
										@RequestParam Integer idGrupo){


        return ResponseEntity.ok().body(esocialService.send(xml, id, empresa, nrInscEmpregador, tpInscEmpregador, nrInscTransmissor, tpInscTransmissor, idGrupo));
    }

    @PostMapping(path = "/consulta-esocial")
    private ResponseEntity<FullResponse> get(@RequestParam String protocolo){


        return ResponseEntity.ok().body(esocialService.get(protocolo));
    }
}

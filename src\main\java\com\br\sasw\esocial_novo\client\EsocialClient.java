package com.br.sasw.esocial_novo.client;

import br.gov.esocial.servicos.empregador.lote.eventos.envio.v1_1_0.EnviarLoteEventos;
import br.gov.esocial.servicos.empregador.lote.eventos.envio.v1_1_0.EnviarLoteEventosResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.soap.client.core.SoapActionCallback;
import org.w3c.dom.Document;
import org.xml.sax.InputSource;


import javax.net.ssl.SSLContext;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.security.KeyStore;

@Service
public class EsocialClient extends WebServiceGatewaySupport {

    @Autowired
    WebServiceTemplate webServiceTemplate;

    @Autowired
    @Qualifier("consultaWebServiceTemplate")
    WebServiceTemplate consultaWebServiceTemplate;

    public EnviarLoteEventosResponse enviarLoteEventos(String xmlLoteEventos) {
        EnviarLoteEventos request = new EnviarLoteEventos();
        
        // Cria o elemento any com o XML do lote de eventos
        org.w3c.dom.Element xmlElement = criarElementoXml(xmlLoteEventos);
        request.setLoteEventos(new EnviarLoteEventos.LoteEventos());
        request.getLoteEventos().setAny(xmlElement);
        
        String soapAction = "http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/v1_1_0/ServicoEnviarLoteEventos/EnviarLoteEventos";
        
        return (EnviarLoteEventosResponse) webServiceTemplate
                .marshalSendAndReceive(
                        "https://webservices.envio.esocial.gov.br/servicos/empregador/enviarloteeventos/WsEnviarLoteEventos.svc",
                        request,
                        new SoapActionCallback(soapAction)
                );
    }

    public String consultarEvento(String protocolo) {
        try {
            // Configuração do RestTemplate com certificado SSL
            RestTemplate restTemplate = createRestTemplateWithSSL();

            // Monta o XML SOAP conforme o curl
            String soapXml = buildConsultaSoapXml(protocolo);

            // Configura os headers
            HttpHeaders headers = new HttpHeaders();
            headers.set("SOAPAction", "http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/consulta/retornoProcessamento/v1_1_0/ServicoConsultarLoteEventos/ConsultarLoteEventos");
            headers.set("Content-Type", "text/xml; charset=utf-8");

            // Cria a requisição
            HttpEntity<String> request = new HttpEntity<>(soapXml, headers);

            // Faz a chamada
            ResponseEntity<String> response = restTemplate.exchange(
                "https://webservices.consulta.esocial.gov.br/servicos/empregador/consultarloteeventos/WsConsultarLoteEventos.svc",
                HttpMethod.POST,
                request,
                String.class
            );

            return response.getBody();

        } catch (Exception e) {
            throw new RuntimeException("Erro ao consultar lote de eventos", e);
        }
    }
    
    private RestTemplate createRestTemplateWithSSL() throws Exception {
        // Configuração do KeyStore (certificado digital)
        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        keyStore.load(new ClassPathResource("go-federal.pfx").getInputStream(), "Federallcom13".toCharArray());

        // Configuração do SSLContext
        SSLContext sslContext = SSLContextBuilder.create()
                .loadKeyMaterial(keyStore, "Federallcom13".toCharArray())
                .loadTrustMaterial(null, (chain, authType) -> true) // Aceita todos os certificados
                .build();

        // Configuração do HttpClient
        SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(sslContext);

        HttpClient httpClient = HttpClients.custom()
                .setSSLSocketFactory(socketFactory)
                .build();

        HttpComponentsClientHttpRequestFactory factory =
                new HttpComponentsClientHttpRequestFactory(httpClient);

        return new RestTemplate(factory);

    }

    private String buildConsultaSoapXml(String protocolo) {
        StringBuilder sb = new StringBuilder();
        sb.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" ")
          .append("xmlns:v1=\"http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/consulta/retornoProcessamento/v1_1_0\">")
          .append("<soapenv:Header/>")
          .append("<soapenv:Body>")
          .append("<v1:ConsultarLoteEventos>")
          .append("<v1:consulta>")
          .append("<v1:protocoloEnvio>")
          .append("<eSocial xmlns=\"http://www.esocial.gov.br/schema/lote/eventos/envio/consulta/retornoProcessamento/v1_0_0\">")
          .append("<consultaLoteEventos>")
          .append("<protocoloEnvio>").append(protocolo).append("</protocoloEnvio>")
          .append("</consultaLoteEventos>")
          .append("</eSocial>")
          .append("</v1:protocoloEnvio>")
          .append("</v1:consulta>")
          .append("</v1:ConsultarLoteEventos>")
          .append("</soapenv:Body>")
          .append("</soapenv:Envelope>");

        return sb.toString();
    }

    private org.w3c.dom.Element criarElementoXml(String xmlString) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));
            return document.getDocumentElement();
        } catch (Exception e) {
            throw new RuntimeException("Erro ao criar elemento XML", e);
        }
    }
}
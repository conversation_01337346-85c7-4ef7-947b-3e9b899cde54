@echo off
setlocal enabledelayedexpansion

echo ========================================
echo  eSocial Novo - Versao Simples
echo ========================================

REM Configuracao do Java local
set "LOCAL_JAVA_HOME=C:\Program Files\Java\jdk-17.0.15+6"
set "MAVEN_HOME=%~dp0tools\maven"

REM Configura as variaveis de ambiente
set "JAVA_HOME=%LOCAL_JAVA_HOME%"
set "PATH=%LOCAL_JAVA_HOME%\bin;%MAVEN_HOME%\bin;%PATH%"
set "M2_HOME=%MAVEN_HOME%"

echo Usando Java: %JAVA_HOME%
echo Usando Maven: %MAVEN_HOME%
echo.

REM Verifica arquivos necessarios
if not exist "pom.xml" (
    echo ERRO: pom.xml nao encontrado!
    pause
    exit /b 1
)

echo ========================================
echo  IMPORTANTE - LEIA ANTES DE CONTINUAR
echo ========================================
echo.
echo Esta e a PRIMEIRA execucao do Maven neste projeto.
echo O Maven precisa baixar TODAS as dependencias da internet.
echo.
echo Isso pode demorar entre 5 a 15 minutos dependendo da sua conexao.
echo.
echo O que voce vera:
echo - Muitas linhas "Downloading from central..."
echo - O processo pode parecer travado por alguns minutos
echo - Isso e NORMAL na primeira execucao
echo.
echo Nas proximas execucoes sera muito mais rapido.
echo.
echo Pressione qualquer tecla para continuar ou Ctrl+C para cancelar...
pause >nul
echo.

REM Limpa diretorio target se existir
if exist "target" (
    echo Limpando compilacao anterior...
    rmdir /s /q "target" 2>nul
)

echo ========================================
echo  Compilando e empacotando...
echo ========================================
echo.
echo AGUARDE: Baixando dependencias e compilando...
echo (Isso pode demorar varios minutos na primeira vez)
echo.

REM Executa tudo de uma vez
"%MAVEN_HOME%\bin\mvn.cmd" clean package -DskipTests -Dmaven.repo.local=.m2\repository

set "BUILD_RESULT=%ERRORLEVEL%"

if %BUILD_RESULT% neq 0 (
    echo.
    echo ========================================
    echo  ERRO na compilacao!
    echo ========================================
    echo.
    echo Codigo de erro: %BUILD_RESULT%
    echo.
    echo Possiveis causas:
    echo 1. Problemas de conectividade com internet
    echo 2. Firewall/antivirus bloqueando downloads
    echo 3. Proxy corporativo nao configurado
    echo 4. Espaco em disco insuficiente
    echo.
    echo Solucoes:
    echo 1. Verifique sua conexao com internet
    echo 2. Tente novamente (pode ser problema temporario)
    echo 3. Execute como Administrador
    echo 4. Configure proxy se necessario
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo  Compilacao bem-sucedida!
echo ========================================

REM Encontra o JAR gerado
set "JAR_FILE="
for %%f in (target\*.jar) do (
    if not "%%~nf"=="%%~nf.original" (
        set "JAR_FILE=%%f"
    )
)

if "%JAR_FILE%"=="" (
    echo ERRO: JAR nao encontrado!
    echo.
    echo Arquivos no diretorio target:
    dir "target" /b 2>nul
    pause
    exit /b 1
)

echo JAR gerado: %JAR_FILE%
echo.

REM Executa a aplicacao
echo ========================================
echo  Iniciando aplicacao...
echo ========================================
echo.
echo A aplicacao Spring Boot esta iniciando...
echo Aguarde ate ver a mensagem "Started EsocialNovoApplication"
echo.
echo Para parar a aplicacao, pressione Ctrl+C
echo.

"%JAVA_HOME%\bin\java.exe" -jar "%JAR_FILE%"

echo.
echo ========================================
echo  Aplicacao finalizada
echo ========================================
pause

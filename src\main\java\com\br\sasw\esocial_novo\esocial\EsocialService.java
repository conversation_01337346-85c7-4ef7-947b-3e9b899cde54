package com.br.sasw.esocial_novo.esocial;

import br.gov.esocial.servicos.empregador.lote.eventos.envio.v1_1_0.EnviarLoteEventosResponse;
import com.br.sasw.esocial_novo.client.EsocialClient;
import com.br.sasw.esocial_novo.util.XmlSigner;
import lombok.RequiredArgsConstructor;
import lombok.Data;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

@Service
@RequiredArgsConstructor
public class EsocialService {

    private final EsocialClient esocialClient;

    @Data
	public class FullResponse {
		
		private EnviarLoteEventosResponse evento;
		private String xmlEnviado;
	}

    public FullResponse send(String xml, String id, String empresa, String nrInscEmpregador, String tpInscEmpregador, String nrInscTransmissor, String tpInscTransmissor, Integer idGrupo){
        String signedXml = XmlSigner.sign(xml, empresa);
        String eventoCompleto = buildEvent(signedXml, id, nrInscEmpregador, tpInscEmpregador, nrInscTransmissor,tpInscTransmissor, idGrupo);
        EnviarLoteEventosResponse response = esocialClient.enviarLoteEventos(eventoCompleto);
		FullResponse res = new FullResponse();
		res.setEvento(response);
		res.setXmlEnviado(eventoCompleto);
        return res;
    }

    private String buildEvent(String xml, String id, String nrInscEmpregador, String tpInscEmpregador, String nrInscTransmissor, String tpInscTransmissor, Integer idGrupo){
        String evento =
                """
                 <eSocial xmlns="http://www.esocial.gov.br/schema/lote/eventos/envio/v1_1_1">
                     <envioLoteEventos grupo="${idGrupo}">
                         <ideEmpregador>
                             <tpInsc>${tpInscEmpregador}</tpInsc>
                             <nrInsc>${nrInscEmpregador}</nrInsc>
                         </ideEmpregador>
                         <ideTransmissor>
                             <tpInsc>${tpInscTransmissor}</tpInsc>
                             <nrInsc>${nrInscTransmissor}</nrInsc>
                         </ideTransmissor>
                         <eventos>
                             <evento Id="${id}">${evento}</evento>
                         </eventos>
                 </envioLoteEventos>
                </eSocial>
                """;
        evento = evento.replace("${evento}", xml);
        evento = evento.replace("${id}", id);
        evento = evento.replace("${nrInscEmpregador}", nrInscEmpregador);
        evento = evento.replace("${tpInscEmpregador}", tpInscEmpregador);
		evento = evento.replace("${nrInscTransmissor}", nrInscTransmissor);
        evento = evento.replace("${tpInscTransmissor}", tpInscTransmissor);
		evento = evento.replace("${idGrupo}", String.valueOf(idGrupo));
		
		System.err.println(evento);
        return evento;
    }

    public FullResponse get(String protocolo) {

        StringBuilder sb = new StringBuilder();

        sb.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" ")
                .append("xmlns:v1=\"http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/consulta/retornoProcessamento/v1_1_0\">")
                .append("<soapenv:Header/>")
                .append("<soapenv:Body>")
                .append("<v1:ConsultarLoteEventos>")
                .append("<v1:consulta>")
                .append("<v1:protocoloEnvio>")
                .append("<eSocial xmlns=\"http://www.esocial.gov.br/schema/lote/eventos/envio/consulta/retornoProcessamento/v1_0_0\">")
                .append("<consultaLoteEventos>")
                .append("<protocoloEnvio>").append(protocolo).append("</protocoloEnvio>")
                .append("</consultaLoteEventos>")
                .append("</eSocial>")
                .append("</v1:protocoloEnvio>")
                .append("</v1:consulta>")
                .append("</v1:ConsultarLoteEventos>")
                .append("</soapenv:Body>")
                .append("</soapenv:Envelope>");

//        EnviarLoteEventosResponse response = esocialClient.consultarEvento(sb.toString());
//        FullResponse res = new FullResponse();
//        res.setEvento(response);
//        res.setXmlEnviado(sb.toString());

        return null;
    }
}

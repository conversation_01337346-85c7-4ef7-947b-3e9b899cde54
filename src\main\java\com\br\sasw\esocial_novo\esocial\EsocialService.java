package com.br.sasw.esocial_novo.esocial;

import br.gov.esocial.servicos.empregador.lote.eventos.envio.v1_1_0.EnviarLoteEventosResponse;

import com.br.sasw.esocial_novo.client.EsocialClient;
import com.br.sasw.esocial_novo.util.XmlSigner;
import lombok.RequiredArgsConstructor;
import lombok.Data;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

@Service
@RequiredArgsConstructor
public class EsocialService {

    private final EsocialClient esocialClient;

    @Data
	public class FullResponse {

		private EnviarLoteEventosResponse evento;
		private String xmlEnviado;
	}

    @Data
    public class ConsultaResponse {

        private String resposta;
        private String xmlEnviado;
    }

    public FullResponse send(String xml, String id, String empresa, String nrInscEmpregador, String tpInscEmpregador, String nrInscTransmissor, String tpInscTransmissor, Integer idGrupo){
        String signedXml = XmlSigner.sign(xml, empresa);
        String eventoCompleto = buildEvent(signedXml, id, nrInscEmpregador, tpInscEmpregador, nrInscTransmissor,tpInscTransmissor, idGrupo);
        EnviarLoteEventosResponse response = esocialClient.enviarLoteEventos(eventoCompleto);
		FullResponse res = new FullResponse();
		res.setEvento(response);
		res.setXmlEnviado(eventoCompleto);
        return res;
    }

    private String buildEvent(String xml, String id, String nrInscEmpregador, String tpInscEmpregador, String nrInscTransmissor, String tpInscTransmissor, Integer idGrupo){
        String evento =
                """
                 <eSocial xmlns="http://www.esocial.gov.br/schema/lote/eventos/envio/v1_1_1">
                     <envioLoteEventos grupo="${idGrupo}">
                         <ideEmpregador>
                             <tpInsc>${tpInscEmpregador}</tpInsc>
                             <nrInsc>${nrInscEmpregador}</nrInsc>
                         </ideEmpregador>
                         <ideTransmissor>
                             <tpInsc>${tpInscTransmissor}</tpInsc>
                             <nrInsc>${nrInscTransmissor}</nrInsc>
                         </ideTransmissor>
                         <eventos>
                             <evento Id="${id}">${evento}</evento>
                         </eventos>
                 </envioLoteEventos>
                </eSocial>
                """;
        evento = evento.replace("${evento}", xml);
        evento = evento.replace("${id}", id);
        evento = evento.replace("${nrInscEmpregador}", nrInscEmpregador);
        evento = evento.replace("${tpInscEmpregador}", tpInscEmpregador);
		evento = evento.replace("${nrInscTransmissor}", nrInscTransmissor);
        evento = evento.replace("${tpInscTransmissor}", tpInscTransmissor);
		evento = evento.replace("${idGrupo}", String.valueOf(idGrupo));
		
		System.err.println(evento);
        return evento;
    }

    public ConsultaResponse get(String protocolo) {
        // Chama o método que usa RestTemplate diretamente
        String response = esocialClient.consultarEvento(protocolo);

        ConsultaResponse res = new ConsultaResponse();
        res.setResposta(response);
        res.setXmlEnviado("Protocolo: " + protocolo);

        return res;
    }
}

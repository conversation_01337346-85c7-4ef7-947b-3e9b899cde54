set "MAVEN_HOME=%~dp0tools\maven"
set "MAVEN_VERSION=3.9.10"

REM Configura Maven no PATH
set "PATH=%MAVEN_HOME%\bin;%PATH%"
set "M2_HOME=%MAVEN_HOME%"

echo Usando Maven: %MAVEN_HOME%
"%MAVEN_HOME%\bin\mvn.cmd" -version
echo.

REM Limpa diretorio target
if exist "target" (
    echo Limpando compilacao anterior...
    rmdir /s /q "target" 2>nul
)

REM Compila a aplicacao
echo ========================================
echo  Compilando a aplicacao...
echo ========================================

"%MAVEN_HOME%\bin\mvn.cmd" clean package -DskipTests

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERRO: Falha na compilacao!
    pause
    exit /b 1
)

echo.
echo ========================================
echo  Compilacao concluida!
echo ========================================

REM Encontra o JAR gerado
set "JAR_FILE="
for %%f in (target\*.jar) do (
    if not "%%~nf"=="%%~nf.original" (
        set "JAR_FILE=%%f"
    )
)

if "%JAR_FILE%"=="" (
    echo ERRO: JAR nao encontrado!
    pause
    exit /b 1
)

echo JAR encontrado: %JAR_FILE%
echo.

REM Executa a aplicacao
echo ========================================
echo  Iniciando a aplicacao...
echo ========================================
echo.
echo Para parar a aplicacao, pressione Ctrl+C
echo.

"%JAVA_HOME%\bin\java.exe" -jar "%JAR_FILE%"

echo.
echo Aplicacao finalizada
pause
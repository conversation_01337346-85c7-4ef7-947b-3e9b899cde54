@echo off
setlocal enabledelayedexpansion

echo ========================================
echo  Teste de Compilacao - Debug
echo ========================================

REM Configuracao do Java local
set "LOCAL_JAVA_HOME=C:\Program Files\Java\jdk-17.0.15+6"
set "MAVEN_HOME=%~dp0tools\maven"

REM Configura as variaveis de ambiente
set "JAVA_HOME=%LOCAL_JAVA_HOME%"
set "PATH=%LOCAL_JAVA_HOME%\bin;%MAVEN_HOME%\bin;%PATH%"
set "M2_HOME=%MAVEN_HOME%"

echo Java: %JAVA_HOME%
echo Maven: %MAVEN_HOME%
echo.

REM Verifica se os arquivos necessarios existem
echo Verificando arquivos necessarios...
if exist "pom.xml" (
    echo [OK] pom.xml encontrado
) else (
    echo [ERRO] pom.xml nao encontrado
    pause
    exit /b 1
)

if exist "src\main\resources\wsdl\WsEnviarLoteEventos.xsd" (
    echo [OK] XSD encontrado: src\main\resources\wsdl\WsEnviarLoteEventos.xsd
) else (
    echo [AVISO] XSD nao encontrado: src\main\resources\wsdl\WsEnviarLoteEventos.xsd
    echo         Isso pode causar problemas na geracao de codigo JAXB
)

echo.

REM Teste 1: Validacao do projeto
echo ========================================
echo  Teste 1: Validacao do projeto
echo ========================================
"%MAVEN_HOME%\bin\mvn.cmd" validate
echo Resultado da validacao: %ERRORLEVEL%
echo.

REM Teste 2: Limpeza
echo ========================================
echo  Teste 2: Limpeza do projeto
echo ========================================
"%MAVEN_HOME%\bin\mvn.cmd" clean
echo Resultado da limpeza: %ERRORLEVEL%
echo.

REM Teste 3: Geracao de codigo (se XSD existir)
if exist "src\main\resources\wsdl\WsEnviarLoteEventos.xsd" (
    echo ========================================
    echo  Teste 3: Geracao de codigo JAXB
    echo ========================================
    "%MAVEN_HOME%\bin\mvn.cmd" generate-sources
    echo Resultado da geracao: %ERRORLEVEL%
    echo.
    
    REM Verifica se o codigo foi gerado
    if exist "target\generated-sources\jaxb" (
        echo [OK] Codigo JAXB gerado em target\generated-sources\jaxb
        dir "target\generated-sources\jaxb" /s /b | find ".java" >nul
        if %ERRORLEVEL% equ 0 (
            echo [OK] Arquivos Java encontrados no codigo gerado
        ) else (
            echo [AVISO] Nenhum arquivo Java encontrado no codigo gerado
        )
    ) else (
        echo [ERRO] Diretorio de codigo gerado nao encontrado
    )
    echo.
)

REM Teste 4: Compilacao apenas
echo ========================================
echo  Teste 4: Compilacao do codigo
echo ========================================
"%MAVEN_HOME%\bin\mvn.cmd" compile
set "COMPILE_RESULT=%ERRORLEVEL%"
echo Resultado da compilacao: %COMPILE_RESULT%
echo.

if %COMPILE_RESULT% equ 0 (
    echo [OK] Compilacao bem-sucedida!
    
    REM Teste 5: Empacotamento
    echo ========================================
    echo  Teste 5: Empacotamento (JAR)
    echo ========================================
    "%MAVEN_HOME%\bin\mvn.cmd" package -DskipTests
    set "PACKAGE_RESULT=%ERRORLEVEL%"
    echo Resultado do empacotamento: %PACKAGE_RESULT%
    
    if %PACKAGE_RESULT% equ 0 (
        echo [OK] Empacotamento bem-sucedido!
        
        REM Verifica JAR gerado
        if exist "target\*.jar" (
            echo.
            echo JARs gerados:
            dir "target\*.jar" /b
        )
    ) else (
        echo [ERRO] Falha no empacotamento
    )
) else (
    echo [ERRO] Falha na compilacao
    echo.
    echo Verifique os erros acima para identificar o problema.
    echo.
    echo Possiveis solucoes:
    echo 1. Verifique se o arquivo XSD existe
    echo 2. Verifique conectividade com repositorios Maven
    echo 3. Execute: mvn dependency:resolve
    echo 4. Limpe cache Maven: mvn dependency:purge-local-repository
)

echo.
echo ========================================
echo  Teste concluido
echo ========================================
echo.
echo Para mais detalhes, execute:
echo mvn compile -X  (modo debug)
echo mvn compile -e  (mostra stack trace)
echo.
pause

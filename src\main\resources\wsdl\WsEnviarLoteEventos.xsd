<?xml version="1.0" encoding="utf-8"?>
<xs:schema elementFormDefault="qualified"
           targetNamespace="http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/v1_1_0"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:tns="http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/v1_1_0">
    <xs:element name="EnviarLoteEventos">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" maxOccurs="1" name="loteEventos">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:any/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="EnviarLoteEventosResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" maxOccurs="1" name="EnviarLoteEventosResult">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:any/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
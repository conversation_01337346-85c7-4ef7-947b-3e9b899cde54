<?xml version="1.0" encoding="utf-8"?>
<xs:schema elementFormDefault="qualified"
           targetNamespace="http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/consulta/retornoProcessamento/v1_0_0"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:tns="http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/consulta/retornoProcessamento/v1_0_0">
    
    <xs:element name="ConsultarLoteEventos">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="consulta" nillable="true">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element minOccurs="0" name="protocoloEnvio">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:any minOccurs="0" processContents="lax" />
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    
    <xs:element name="ConsultarLoteEventosResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="ConsultarLoteEventosResult" nillable="true">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:any minOccurs="0" processContents="lax" />
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>

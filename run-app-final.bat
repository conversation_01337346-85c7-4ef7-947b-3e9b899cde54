@echo off
setlocal enabledelayedexpansion

echo ========================================
echo  eSocial Novo - Execucao Final
echo ========================================

REM Configuracao do Java local
set "LOCAL_JAVA_HOME=C:\Program Files\Java\jdk-17.0.15+6"
set "MAVEN_HOME=%~dp0tools\maven"

REM Configura as variaveis de ambiente
set "JAVA_HOME=%LOCAL_JAVA_HOME%"
set "PATH=%LOCAL_JAVA_HOME%\bin;%MAVEN_HOME%\bin;%PATH%"
set "M2_HOME=%MAVEN_HOME%"

echo Usando Java: %JAVA_HOME%
echo Usando Maven: %MAVEN_HOME%
echo.

REM Verifica se JAR ja existe
if exist "target\esocial-novo-0.0.1-SNAPSHOT.jar" (
    echo [OK] JAR ja compilado encontrado!
    echo Arquivo: target\esocial-novo-0.0.1-SNAPSHOT.jar
    echo.
    
    set /p RECOMPILE="Deseja recompilar? (s/N): "
    if /i "!RECOMPILE!"=="s" goto COMPILE
    if /i "!RECOMPILE!"=="sim" goto COMPILE
    goto RUN_APP
) else (
    echo JAR nao encontrado. Compilando...
    goto COMPILE
)

:COMPILE
echo ========================================
echo  Compilando aplicacao...
echo ========================================
echo.
echo Aguarde... (pode demorar alguns minutos)
echo.

REM Limpa diretorio target
if exist "target" (
    rmdir /s /q "target" 2>nul
)

REM Compila
"%MAVEN_HOME%\bin\mvn.cmd" clean package -DskipTests -Dmaven.repo.local=.m2\repository

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERRO: Falha na compilacao!
    pause
    exit /b 1
)

REM Verifica se JAR foi gerado
if not exist "target\esocial-novo-0.0.1-SNAPSHOT.jar" (
    echo ERRO: JAR nao foi gerado!
    pause
    exit /b 1
)

echo.
echo [OK] Compilacao concluida com sucesso!
echo.

:RUN_APP
echo ========================================
echo  Iniciando aplicacao eSocial Novo
echo ========================================
echo.
echo Arquivo JAR: target\esocial-novo-0.0.1-SNAPSHOT.jar
echo Java: %JAVA_HOME%
echo.
echo IMPORTANTE:
echo - A aplicacao pode demorar 30-60 segundos para iniciar
echo - Aguarde a mensagem "Started EsocialNovoApplication"
echo - A aplicacao ficara disponivel em http://localhost:8080
echo - Para parar a aplicacao, pressione Ctrl+C
echo.
echo Iniciando...
echo.

REM Executa a aplicacao
"%JAVA_HOME%\bin\java.exe" -jar "target\esocial-novo-0.0.1-SNAPSHOT.jar"

echo.
echo ========================================
echo  Aplicacao finalizada
echo ========================================
echo.
echo A aplicacao foi encerrada.
echo.
pause

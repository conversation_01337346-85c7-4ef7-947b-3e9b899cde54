@echo off

REM Verifica se esta executando como administrador
net session >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Este script precisa ser executado como Administrador.
    echo Clique com botao direito no script e selecione "Executar como administrador"
    echo.
    echo Ou execute o comando abaixo em um prompt de comando como administrador:
    echo %~dp0run-app.bat
    echo.
    pause
    exit /b 1
)

echo ========================================
echo  Executando como Administrador
echo ========================================

REM Chama o script principal
call "%~dp0run-app.bat"

<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions name="ServicoEnviarLoteEventos"
                  targetNamespace="http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/v1_1_0"
                  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex"
                  xmlns:i0="http://www.esocial.gov.br/servicos/empregador/enviarloteeventos/v1_1_0"
                  xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
                  xmlns:wsa10="http://www.w3.org/2005/08/addressing"
                  xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy"
                  xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy"
                  xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract"
                  xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/"
                  xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing"
                  xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:tns="http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/v1_1_0"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl"
                  xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/">
    <wsdl:import namespace="http://www.esocial.gov.br/servicos/empregador/enviarloteeventos/v1_1_0"
                 location="WsEnviarLoteEventos0.wsdl"/>
    <wsdl:types>
        <xsd:schema targetNamespace="http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/v1_1_0/Imports">
            <xsd:import
                    schemaLocation="WsEnviarLoteEventos.xsd"
                    namespace="http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/v1_1_0"/>
        </xsd:schema>
    </wsdl:types>
    <wsdl:message name="ServicoEnviarLoteEventos_EnviarLoteEventos_InputMessage">
        <wsdl:part name="parameters" element="tns:EnviarLoteEventos"/>
    </wsdl:message>
    <wsdl:message name="ServicoEnviarLoteEventos_EnviarLoteEventos_OutputMessage">
        <wsdl:part name="parameters" element="tns:EnviarLoteEventosResponse"/>
    </wsdl:message>
    <wsdl:portType name="ServicoEnviarLoteEventos">
        <wsdl:operation name="EnviarLoteEventos">
            <wsdl:input
                    wsaw:Action="http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/v1_1_0/ServicoEnviarLoteEventos/EnviarLoteEventos"
                    message="tns:ServicoEnviarLoteEventos_EnviarLoteEventos_InputMessage"/>
            <wsdl:output
                    wsaw:Action="http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/v1_1_0/ServicoEnviarLoteEventos/EnviarLoteEventosResponse"
                    message="tns:ServicoEnviarLoteEventos_EnviarLoteEventos_OutputMessage"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:service name="ServicoEnviarLoteEventos">
        <wsdl:port name="WsEnviarLoteEventos" binding="i0:WsEnviarLoteEventos">
            <soap:address
                    location="https://webservices.envio.esocial.gov.br/servicos/empregador/enviarloteeventos/WsEnviarLoteEventos.svc"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>